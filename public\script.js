class QuotexMonitorDashboard {
    constructor() {
        this.ws = null;
        this.currentPage = 1;
        this.pageSize = 50;
        this.filters = {
            type: '',
            direction: '',
            session: ''
        };
        this.searchTerm = '';
        this.isMonitoring = false;
        
        this.initializeElements();
        this.attachEventListeners();
        this.connectWebSocket();
        this.loadInitialData();
    }

    initializeElements() {
        // Buttons
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.refreshBtn = document.getElementById('refreshBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.prevPageBtn = document.getElementById('prevPage');
        this.nextPageBtn = document.getElementById('nextPage');

        // Filters
        this.typeFilter = document.getElementById('typeFilter');
        this.directionFilter = document.getElementById('directionFilter');
        this.sessionFilter = document.getElementById('sessionFilter');
        this.searchInput = document.getElementById('searchInput');

        // Display elements
        this.statusText = document.getElementById('statusText');
        this.statusDot = document.getElementById('statusDot');
        this.totalMessages = document.getElementById('totalMessages');
        this.incomingMessages = document.getElementById('incomingMessages');
        this.outgoingMessages = document.getElementById('outgoingMessages');
        this.systemMessages = document.getElementById('systemMessages');
        this.messagesBody = document.getElementById('messagesBody');
        this.pageInfo = document.getElementById('pageInfo');

        // Modal elements
        this.modal = document.getElementById('messageModal');
        this.modalTimestamp = document.getElementById('modalTimestamp');
        this.modalType = document.getElementById('modalType');
        this.modalDirection = document.getElementById('modalDirection');
        this.modalSize = document.getElementById('modalSize');
        this.modalUrl = document.getElementById('modalUrl');
        this.modalContent = document.getElementById('modalContent');

        // Loading overlay
        this.loadingOverlay = document.getElementById('loadingOverlay');
    }

    attachEventListeners() {
        // Control buttons
        this.startBtn.addEventListener('click', () => this.startMonitoring());
        this.stopBtn.addEventListener('click', () => this.stopMonitoring());
        this.refreshBtn.addEventListener('click', () => this.loadMessages());
        this.clearBtn.addEventListener('click', () => this.clearMessages());

        // Pagination
        this.prevPageBtn.addEventListener('click', () => this.previousPage());
        this.nextPageBtn.addEventListener('click', () => this.nextPage());

        // Filters
        this.typeFilter.addEventListener('change', () => this.applyFilters());
        this.directionFilter.addEventListener('change', () => this.applyFilters());
        this.sessionFilter.addEventListener('change', () => this.applyFilters());

        // Search
        this.searchInput.addEventListener('input', this.debounce(() => this.applySearch(), 500));

        // Modal
        this.modal.querySelector('.close-btn').addEventListener('click', () => this.closeModal());
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.closeModal();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') this.closeModal();
        });
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.showToast('تم الاتصال بالخادم', 'success');
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.showToast('انقطع الاتصال بالخادم', 'error');
            // Attempt to reconnect after 3 seconds
            setTimeout(() => this.connectWebSocket(), 3000);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.showToast('خطأ في الاتصال', 'error');
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'status':
                this.updateStatus(data.data);
                break;
            case 'newMessage':
                this.addNewMessage(data.data);
                break;
            case 'sessionStarted':
                this.onSessionStarted(data.data);
                break;
            case 'sessionEnded':
                this.onSessionEnded(data.data);
                break;
        }
    }

    async loadInitialData() {
        await this.loadSessions();
        await this.loadMessages();
        await this.loadStatistics();
    }

    async startMonitoring() {
        this.showLoading(true);
        try {
            const response = await fetch('/api/start-monitoring', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast('تم بدء المراقبة بنجاح', 'success');
                this.isMonitoring = true;
                this.updateControlButtons();
            } else {
                throw new Error(result.error || 'فشل في بدء المراقبة');
            }
        } catch (error) {
            console.error('Error starting monitoring:', error);
            this.showToast('خطأ في بدء المراقبة: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async stopMonitoring() {
        this.showLoading(true);
        try {
            const response = await fetch('/api/stop-monitoring', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast('تم إيقاف المراقبة', 'warning');
                this.isMonitoring = false;
                this.updateControlButtons();
            } else {
                throw new Error(result.error || 'فشل في إيقاف المراقبة');
            }
        } catch (error) {
            console.error('Error stopping monitoring:', error);
            this.showToast('خطأ في إيقاف المراقبة: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async loadMessages() {
        this.showLoading(true);
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                ...this.filters
            });
            
            if (this.searchTerm) {
                params.append('search', this.searchTerm);
            }
            
            const response = await fetch(`/api/messages?${params}`);
            const data = await response.json();
            
            this.renderMessages(data.messages);
            this.updatePagination(data.pagination);
        } catch (error) {
            console.error('Error loading messages:', error);
            this.showToast('خطأ في تحميل الرسائل', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async loadSessions() {
        try {
            const response = await fetch('/api/sessions');
            const sessions = await response.json();
            
            this.sessionFilter.innerHTML = '<option value="">الجلسة الحالية</option>';
            sessions.forEach(session => {
                const option = document.createElement('option');
                option.value = session.id;
                option.textContent = `${session.id} (${new Date(session.start_time).toLocaleString('ar-SA')})`;
                this.sessionFilter.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading sessions:', error);
        }
    }

    async loadStatistics() {
        try {
            const params = new URLSearchParams();
            if (this.filters.session) {
                params.append('session', this.filters.session);
            }
            
            const response = await fetch(`/api/statistics?${params}`);
            const stats = await response.json();
            
            this.updateStatistics(stats);
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    updateStatistics(stats) {
        this.totalMessages.textContent = stats.total.toLocaleString('ar-SA');
        this.incomingMessages.textContent = stats.byDirection.incoming.toLocaleString('ar-SA');
        this.outgoingMessages.textContent = stats.byDirection.outgoing.toLocaleString('ar-SA');
        this.systemMessages.textContent = stats.byDirection.system.toLocaleString('ar-SA');
    }

    renderMessages(messages) {
        if (!messages || messages.length === 0) {
            this.messagesBody.innerHTML = '<tr class="no-data"><td colspan="6">لا توجد رسائل للعرض</td></tr>';
            return;
        }

        this.messagesBody.innerHTML = messages.map(message => `
            <tr onclick="dashboard.showMessageDetails(${message.id})">
                <td>${new Date(message.timestamp).toLocaleString('ar-SA')}</td>
                <td><span class="type-badge">${this.getTypeLabel(message.type)}</span></td>
                <td><span class="direction-badge direction-${message.direction}">${this.getDirectionLabel(message.direction)}</span></td>
                <td>${this.formatSize(message.size)}</td>
                <td class="content-preview">${this.truncateContent(message.content)}</td>
                <td>
                    <button class="btn btn-primary" onclick="event.stopPropagation(); dashboard.showMessageDetails(${message.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    getTypeLabel(type) {
        const labels = {
            'websocket_message': 'رسالة WebSocket',
            'http_request': 'طلب HTTP',
            'http_response': 'رد HTTP',
            'websocket_open': 'فتح WebSocket',
            'websocket_close': 'إغلاق WebSocket',
            'websocket_error': 'خطأ WebSocket'
        };
        return labels[type] || type;
    }

    getDirectionLabel(direction) {
        const labels = {
            'incoming': 'واردة',
            'outgoing': 'صادرة',
            'system': 'النظام'
        };
        return labels[direction] || direction;
    }

    formatSize(size) {
        if (size < 1024) return `${size} بايت`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} كيلوبايت`;
        return `${(size / (1024 * 1024)).toFixed(1)} ميجابايت`;
    }

    truncateContent(content) {
        if (!content) return '';
        const text = typeof content === 'string' ? content : JSON.stringify(content);
        return text.length > 100 ? text.substring(0, 100) + '...' : text;
    }

    updatePagination(pagination) {
        this.pageInfo.textContent = `صفحة ${pagination.page} من ${pagination.pages}`;
        this.prevPageBtn.disabled = pagination.page <= 1;
        this.nextPageBtn.disabled = pagination.page >= pagination.pages;
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.loadMessages();
        }
    }

    nextPage() {
        this.currentPage++;
        this.loadMessages();
    }

    applyFilters() {
        this.filters.type = this.typeFilter.value;
        this.filters.direction = this.directionFilter.value;
        this.filters.session = this.sessionFilter.value;
        this.currentPage = 1;
        this.loadMessages();
        this.loadStatistics();
    }

    applySearch() {
        this.searchTerm = this.searchInput.value.trim();
        this.currentPage = 1;
        this.loadMessages();
    }

    clearMessages() {
        this.messagesBody.innerHTML = '<tr class="no-data"><td colspan="6">لا توجد رسائل للعرض</td></tr>';
        this.updateStatistics({ total: 0, byDirection: { incoming: 0, outgoing: 0, system: 0 } });
    }

    updateStatus(status) {
        this.isMonitoring = status.isMonitoring;
        this.statusText.textContent = status.isMonitoring ? 'متصل ومراقب' : 'غير متصل';
        this.statusDot.className = `status-dot ${status.isMonitoring ? 'online' : 'offline'}`;
        this.updateControlButtons();
        
        if (status.stats) {
            this.updateStatistics({
                total: status.stats.total,
                byDirection: {
                    incoming: status.stats.incoming,
                    outgoing: status.stats.outgoing,
                    system: 0
                }
            });
        }
    }

    updateControlButtons() {
        this.startBtn.disabled = this.isMonitoring;
        this.stopBtn.disabled = !this.isMonitoring;
    }

    addNewMessage(message) {
        // Update statistics in real-time
        const currentTotal = parseInt(this.totalMessages.textContent.replace(/,/g, '')) || 0;
        this.totalMessages.textContent = (currentTotal + 1).toLocaleString('ar-SA');
        
        if (message.direction === 'incoming') {
            const current = parseInt(this.incomingMessages.textContent.replace(/,/g, '')) || 0;
            this.incomingMessages.textContent = (current + 1).toLocaleString('ar-SA');
        } else if (message.direction === 'outgoing') {
            const current = parseInt(this.outgoingMessages.textContent.replace(/,/g, '')) || 0;
            this.outgoingMessages.textContent = (current + 1).toLocaleString('ar-SA');
        } else if (message.direction === 'system') {
            const current = parseInt(this.systemMessages.textContent.replace(/,/g, '')) || 0;
            this.systemMessages.textContent = (current + 1).toLocaleString('ar-SA');
        }
        
        // Refresh messages if on first page
        if (this.currentPage === 1) {
            this.loadMessages();
        }
        
        // Show notification for important messages
        if (message.type === 'websocket_message' || message.type === 'websocket_error') {
            this.showToast(`رسالة جديدة: ${this.getTypeLabel(message.type)}`, 'info');
        }
    }

    async showMessageDetails(messageId) {
        try {
            const response = await fetch(`/api/messages?id=${messageId}`);
            const data = await response.json();
            
            if (data.messages && data.messages.length > 0) {
                const message = data.messages[0];
                
                this.modalTimestamp.textContent = new Date(message.timestamp).toLocaleString('ar-SA');
                this.modalType.textContent = this.getTypeLabel(message.type);
                this.modalDirection.textContent = this.getDirectionLabel(message.direction);
                this.modalSize.textContent = this.formatSize(message.size);
                this.modalUrl.textContent = message.url || 'غير محدد';
                
                let content = message.content;
                if (typeof content === 'string') {
                    try {
                        content = JSON.stringify(JSON.parse(content), null, 2);
                    } catch (e) {
                        // Keep as string if not valid JSON
                    }
                }
                this.modalContent.textContent = content || 'لا يوجد محتوى';
                
                this.modal.style.display = 'block';
            }
        } catch (error) {
            console.error('Error loading message details:', error);
            this.showToast('خطأ في تحميل تفاصيل الرسالة', 'error');
        }
    }

    closeModal() {
        this.modal.style.display = 'none';
    }

    onSessionStarted(data) {
        this.showToast(`بدأت جلسة جديدة: ${data.sessionId}`, 'success');
        this.loadSessions();
    }

    onSessionEnded(data) {
        this.showToast(`انتهت الجلسة: ${data.sessionId}`, 'warning');
        this.loadSessions();
    }

    showLoading(show) {
        this.loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        document.getElementById('toastContainer').appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new QuotexMonitorDashboard();
});
