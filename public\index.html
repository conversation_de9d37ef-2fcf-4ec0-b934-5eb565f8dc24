<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقب منصة Quotex - Quotex Platform Monitor</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> مراقب منصة Quotex</h1>
                <div class="header-controls">
                    <button id="startBtn" class="btn btn-success">
                        <i class="fas fa-play"></i> بدء المراقبة
                    </button>
                    <button id="stopBtn" class="btn btn-danger" disabled>
                        <i class="fas fa-stop"></i> إيقاف المراقبة
                    </button>
                    <div class="status-indicator">
                        <span id="statusText">غير متصل</span>
                        <div id="statusDot" class="status-dot offline"></div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Statistics Dashboard -->
        <section class="dashboard">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalMessages">0</h3>
                        <p>إجمالي الرسائل</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon incoming">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="incomingMessages">0</h3>
                        <p>رسائل واردة</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon outgoing">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="outgoingMessages">0</h3>
                        <p>رسائل صادرة</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon system">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="systemMessages">0</h3>
                        <p>رسائل النظام</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Filters and Controls -->
        <section class="controls">
            <div class="filters">
                <div class="filter-group">
                    <label for="typeFilter">نوع الرسالة:</label>
                    <select id="typeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="websocket_message">رسائل WebSocket</option>
                        <option value="http_request">طلبات HTTP</option>
                        <option value="http_response">ردود HTTP</option>
                        <option value="websocket_open">فتح WebSocket</option>
                        <option value="websocket_close">إغلاق WebSocket</option>
                        <option value="websocket_error">أخطاء WebSocket</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="directionFilter">الاتجاه:</label>
                    <select id="directionFilter">
                        <option value="">جميع الاتجاهات</option>
                        <option value="incoming">واردة</option>
                        <option value="outgoing">صادرة</option>
                        <option value="system">النظام</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="sessionFilter">الجلسة:</label>
                    <select id="sessionFilter">
                        <option value="">الجلسة الحالية</option>
                    </select>
                </div>
                
                <button id="refreshBtn" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> تحديث
                </button>
                
                <button id="clearBtn" class="btn btn-warning">
                    <i class="fas fa-trash"></i> مسح الشاشة
                </button>
            </div>
        </section>

        <!-- Messages Table -->
        <section class="messages-section">
            <div class="section-header">
                <h2><i class="fas fa-list"></i> سجل الرسائل</h2>
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="البحث في المحتوى...">
                    <i class="fas fa-search"></i>
                </div>
            </div>
            
            <div class="table-container">
                <table id="messagesTable" class="messages-table">
                    <thead>
                        <tr>
                            <th>الوقت</th>
                            <th>النوع</th>
                            <th>الاتجاه</th>
                            <th>الحجم</th>
                            <th>المحتوى</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="messagesBody">
                        <tr class="no-data">
                            <td colspan="6">لا توجد رسائل للعرض</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="pagination">
                <button id="prevPage" class="btn btn-secondary" disabled>
                    <i class="fas fa-chevron-right"></i> السابق
                </button>
                <span id="pageInfo">صفحة 1 من 1</span>
                <button id="nextPage" class="btn btn-secondary" disabled>
                    التالي <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        </section>

        <!-- Message Details Modal -->
        <div id="messageModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تفاصيل الرسالة</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="message-details">
                        <div class="detail-row">
                            <strong>الوقت:</strong>
                            <span id="modalTimestamp"></span>
                        </div>
                        <div class="detail-row">
                            <strong>النوع:</strong>
                            <span id="modalType"></span>
                        </div>
                        <div class="detail-row">
                            <strong>الاتجاه:</strong>
                            <span id="modalDirection"></span>
                        </div>
                        <div class="detail-row">
                            <strong>الحجم:</strong>
                            <span id="modalSize"></span>
                        </div>
                        <div class="detail-row">
                            <strong>الرابط:</strong>
                            <span id="modalUrl"></span>
                        </div>
                        <div class="detail-row full-width">
                            <strong>المحتوى:</strong>
                            <pre id="modalContent"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="spinner"></div>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="script.js"></script>
</body>
</html>
