/**
 * كلاس الاتصال الأساسي مع منصة Quotex باستخدام Puppeteer
 * Core Puppeteer Connector for Quotex Platform
 */

const EventEmitter = require('events');
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const config = require('../config/config');
const Logger = require('../utils/Logger');

// استخدام plugin التخفي لتجنب الكشف
puppeteer.use(StealthPlugin());

class QuotexConnector extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      ...config.quotex,
      headless: options.headless !== false,
      userDataDir: options.userDataDir || './user_data',
      ...options
    };

    this.browser = null;
    this.page = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.sessionId = null;
    this.wsConnection = null;
    this.requestId = 1;
    this.pendingRequests = new Map();

    this.logger = new Logger('QuotexConnector');

    // ربط الأحداث
    this.setupEventHandlers();
  }

  /**
   * إعداد معالجات الأحداث
   */
  setupEventHandlers() {
    this.on('connected', () => {
      this.logger.info('تم الاتصال بنجاح مع منصة Quotex');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
    });

    this.on('disconnected', () => {
      this.logger.warn('تم قطع الاتصال مع منصة Quotex');
      this.isConnected = false;
      this.stopHeartbeat();
      this.handleReconnection();
    });

    this.on('error', (error) => {
      this.logger.error('خطأ في الاتصال:', error);
    });
  }

  /**
   * الاتصال بمنصة Quotex باستخدام Puppeteer
   */
  async connect() {
    try {
      this.logger.info('محاولة الاتصال بمنصة Quotex باستخدام Puppeteer...');

      // إطلاق المتصفح
      this.browser = await puppeteer.launch({
        headless: this.options.headless,
        defaultViewport: null,
        userDataDir: this.options.userDataDir,
        args: [
          '--start-maximized',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      // إنشاء صفحة جديدة
      this.page = await this.browser.newPage();

      // تعيين User Agent
      await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

      // تفعيل اعتراض الطلبات لمراقبة WebSocket
      await this.page.setRequestInterception(true);

      // مراقبة طلبات الشبكة
      this.page.on('request', (request) => {
        const url = request.url();
        if (url.includes('socket.io') || url.includes('ws://') || url.includes('wss://')) {
          this.logger.debug('WebSocket request detected:', url);
        }
        request.continue();
      });

      // الانتقال إلى منصة التداول
      this.logger.info('الانتقال إلى منصة Quotex...');
      await this.page.goto('https://qxbroker.com/ar/demo-trade', {
        waitUntil: 'networkidle2',
        timeout: this.options.timeout
      });

      // انتظار تحميل الصفحة
      await this.page.waitForTimeout(5000);

      // البحث عن WebSocket connection في الصفحة
      await this.setupWebSocketConnection();

      this.isConnected = true;
      this.emit('connected');

      this.logger.info('تم الاتصال بنجاح مع منصة Quotex');
      return true;

    } catch (error) {
      this.logger.error('فشل في الاتصال:', error);
      throw error;
    }
  }

  /**
   * إعداد اتصال WebSocket داخل الصفحة
   */
  async setupWebSocketConnection() {
    try {
      this.logger.info('فحص حالة تسجيل الدخول...');

      // فحص إذا كان المستخدم مسجل دخول بالفعل
      const sessionId = await this.extractSessionId();

      if (sessionId) {
        this.sessionId = sessionId;
        this.logger.info('تم العثور على session ID موجود');

        // إعداد Socket.IO مع session ID
        await this.setupSocketWithSession(sessionId);
      } else {
        this.logger.info('لم يتم العثور على session ID');
        this.logger.info('🔑 يرجى تسجيل الدخول في المتصفح المفتوح...');

        // انتظار تسجيل الدخول اليدوي
        await this.waitForManualLogin();
      }

    } catch (error) {
      this.logger.error('خطأ في إعداد WebSocket:', error);
      // لا نرمي خطأ هنا، بل نتابع بدون Socket.IO
      this.logger.warn('سيتم المتابعة بدون Socket.IO');
    }
  }

  /**
   * استخراج session ID من الصفحة
   */
  async extractSessionId() {
    try {
      const sessionId = await this.page.evaluate(() => {
        // البحث عن session ID في localStorage
        const sessionData = localStorage.getItem('session') ||
                           localStorage.getItem('sessionId') ||
                           localStorage.getItem('user_session');

        if (sessionData) {
          try {
            const parsed = JSON.parse(sessionData);
            return parsed.session || parsed.sessionId || parsed.id;
          } catch {
            return sessionData;
          }
        }

        // البحث في cookies
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name.includes('session') || name.includes('ssid')) {
            return value;
          }
        }

        // البحث في window object
        if (window.session) return window.session;
        if (window.sessionId) return window.sessionId;
        if (window.user && window.user.session) return window.user.session;

        return null;
      });

      return sessionId;
    } catch (error) {
      this.logger.error('خطأ في استخراج session ID:', error);
      return null;
    }
  }

  /**
   * انتظار تسجيل الدخول اليدوي
   */
  async waitForManualLogin() {
    this.logger.info('⏳ انتظار تسجيل الدخول في المتصفح...');
    this.logger.info('💡 نصيحة: سجل دخول إلى حسابك أو استخدم الحساب التجريبي');

    let attempts = 0;
    const maxAttempts = 24; // 2 دقيقة (24 * 5 ثواني)

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 5000)); // انتظار 5 ثواني

      // محاولة استخراج session ID
      const sessionId = await this.extractSessionId();
      if (sessionId) {
        this.sessionId = sessionId;
        this.logger.info('✅ تم تسجيل الدخول بنجاح!');
        await this.setupSocketWithSession(sessionId);
        return;
      }

      attempts++;
      if (attempts % 6 === 0) { // كل 30 ثانية
        this.logger.info(`⏳ لا يزال في انتظار تسجيل الدخول... (${Math.floor(attempts/6)}/4 دقائق)`);
      }
    }

    this.logger.warn('⚠️ انتهت مهلة انتظار تسجيل الدخول');
    this.logger.info('💡 يمكنك المتابعة يدوياً أو إعادة تشغيل الاختبار');
  }

  /**
   * إعداد Socket.IO مع session ID
   */
  async setupSocketWithSession(sessionId) {
    try {
      this.logger.info('إعداد Socket.IO مع session ID...');

      await this.page.evaluate((session) => {
        // إنشاء اتصال Socket.IO
        window.quotexSocket = io('wss://ws.qxbroker.com', {
          transports: ['websocket'],
          upgrade: true,
          rememberUpgrade: true
        });

        // تخزين الأحداث
        window.quotexEvents = [];
        window.quotexResponses = new Map();

        // معالج الاتصال
        window.quotexSocket.on('connect', () => {
          console.log('Socket.IO متصل');

          // إرسال أمر التفويض
          window.quotexSocket.emit('authorization', {
            session: session,
            isDemo: 1,
            tournamentId: 0
          });
        });

        // معالج الأحداث العام
        window.quotexSocket.onAny((eventName, ...args) => {
          window.quotexEvents.push({
            event: eventName,
            data: args,
            timestamp: Date.now()
          });

          // حفظ الاستجابات للطلبات
          if (args[0] && typeof args[0] === 'object') {
            window.quotexResponses.set(eventName, args[0]);
          }

          // الاحتفاظ بآخر 1000 حدث فقط
          if (window.quotexEvents.length > 1000) {
            window.quotexEvents = window.quotexEvents.slice(-1000);
          }
        });

        // معالج الأخطاء
        window.quotexSocket.on('connect_error', (error) => {
          console.error('خطأ في Socket.IO:', error);
        });

        window.quotexSocket.on('disconnect', (reason) => {
          console.log('تم قطع Socket.IO:', reason);
        });

      }, sessionId);

      // انتظار الاتصال
      await this.page.waitForFunction(() => {
        return window.quotexSocket && window.quotexSocket.connected;
      }, { timeout: 30000 });

      this.logger.info('تم إعداد Socket.IO بنجاح');

    } catch (error) {
      this.logger.error('خطأ في إعداد Socket.IO:', error);
      throw error;
    }
  }

  /**
   * انتظار تأسيس الاتصال
   */
  waitForConnection() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('انتهت مهلة الاتصال'));
      }, this.options.timeout);

      this.once('connected', () => {
        clearTimeout(timeout);
        resolve();
      });

      this.once('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * معالجة أحداث Socket.IO
   */
  handleSocketEvent(eventName, args) {
    try {
      this.logger.debug(`حدث Socket.IO: ${eventName}`, args);

      // معالجة الأحداث المختلفة
      switch (eventName) {
        case 'instruments/list':
        case 'balance/list':
        case 'chart_notification/get':
        case 'orders/opened/list':
        case 'orders/closed/list':
          this.emit(eventName, args[0]);
          break;
        case 'quotes/stream':
          this.emit('priceUpdate', args[0]);
          break;
        case 'chart_notification':
          this.emit('candleUpdate', args[0]);
          break;
        default:
          this.emit('socketEvent', { event: eventName, data: args });
      }
    } catch (error) {
      this.logger.error('خطأ في معالجة حدث Socket.IO:', error);
    }
  }

  /**
   * معالجة الرسائل الواردة
   */
  handleMessage(data) {
    try {
      let message;
      if (typeof data === 'string') {
        message = JSON.parse(data);
      } else {
        message = data;
      }

      // معالجة أنواع الرسائل المختلفة
      if (message.event) {
        this.handleEvent(message);
      } else if (message.requestId && this.pendingRequests.has(message.requestId)) {
        this.handleResponse(message);
      } else {
        this.emit('message', message);
      }
    } catch (error) {
      this.logger.error('خطأ في معالجة الرسالة:', error);
    }
  }

  /**
   * معالجة الأحداث
   */
  handleEvent(message) {
    switch (message.event) {
      case 'authorization':
        this.handleAuthorization(message);
        break;
      case 'quotes/stream':
        this.emit('priceUpdate', message.data);
        break;
      case 'chart_notification':
        this.emit('candleUpdate', message.data);
        break;
      case 'orders/opened':
        this.emit('orderOpened', message.data);
        break;
      case 'orders/closed':
        this.emit('orderClosed', message.data);
        break;
      default:
        this.emit('event', message);
    }
  }

  /**
   * معالجة الاستجابات
   */
  handleResponse(message) {
    const request = this.pendingRequests.get(message.requestId);
    if (request) {
      this.pendingRequests.delete(message.requestId);
      
      if (message.error) {
        request.reject(new Error(message.error));
      } else {
        request.resolve(message.data);
      }
    }
  }

  /**
   * معالجة التفويض
   */
  handleAuthorization(message) {
    if (message.data && message.data.session) {
      this.sessionId = message.data.session;
      this.logger.info('تم التفويض بنجاح');
      this.emit('authorized', message.data);
    }
  }

  /**
   * إرسال أمر Socket.IO عبر الصفحة
   */
  async send(event, data = {}) {
    if (!this.isConnected || !this.page) {
      throw new Error('غير متصل بالمنصة');
    }

    try {
      // إرسال الحدث
      await this.page.evaluate((eventName, eventData) => {
        if (window.quotexSocket && window.quotexSocket.connected) {
          window.quotexSocket.emit(eventName, eventData);
        } else {
          throw new Error('Socket.IO غير متصل');
        }
      }, event, data);

      // انتظار الاستجابة
      const result = await this.page.waitForFunction((eventName) => {
        return window.quotexResponses && window.quotexResponses.has(eventName);
      }, { timeout: 30000 }, event);

      if (result) {
        const response = await this.page.evaluate((eventName) => {
          const response = window.quotexResponses.get(eventName);
          window.quotexResponses.delete(eventName); // مسح الاستجابة بعد قراءتها
          return response;
        }, event);

        return response;
      }

      throw new Error('لم يتم استلام استجابة');

    } catch (error) {
      this.logger.error(`خطأ في إرسال الحدث ${event}:`, error);
      throw error;
    }
  }

  /**
   * إرسال أمر بدون انتظار استجابة
   */
  async emit(event, data = {}) {
    if (!this.isConnected || !this.page) {
      throw new Error('غير متصل بالمنصة');
    }

    try {
      await this.page.evaluate((eventName, eventData) => {
        if (window.quotexSocket) {
          window.quotexSocket.emit(eventName, eventData);
        }
      }, event, data);
    } catch (error) {
      this.logger.error(`خطأ في إرسال الحدث ${event}:`, error);
      throw error;
    }
  }

  /**
   * جلب الأحداث الجديدة من الصفحة
   */
  async getNewEvents() {
    if (!this.page) return [];

    try {
      const events = await this.page.evaluate(() => {
        const events = window.quotexEvents || [];
        window.quotexEvents = []; // مسح الأحداث بعد جلبها
        return events;
      });

      return events;
    } catch (error) {
      this.logger.error('خطأ في جلب الأحداث:', error);
      return [];
    }
  }

  /**
   * بدء نبضات القلب
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.socket) {
        this.socket.emit('ping');
      }
    }, 30000); // كل 30 ثانية
  }

  /**
   * إيقاف نبضات القلب
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * معالجة إعادة الاتصال
   */
  async handleReconnection() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.logger.error('تم الوصول للحد الأقصى من محاولات إعادة الاتصال');
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    this.logger.info(`محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.options.maxReconnectAttempts}`);

    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        this.logger.error('فشل في إعادة الاتصال:', error);
      }
    }, this.options.reconnectInterval);
  }

  /**
   * قطع الاتصال
   */
  async disconnect() {
    this.logger.info('قطع الاتصال مع منصة Quotex...');

    this.isConnected = false;

    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    } catch (error) {
      this.logger.error('خطأ في قطع الاتصال:', error);
    }

    this.sessionId = null;
    this.pendingRequests.clear();
    this.emit('disconnected');
  }

  /**
   * التحقق من حالة الاتصال
   */
  isConnectionActive() {
    return this.isConnected && this.browser && this.page && !this.page.isClosed();
  }

  /**
   * الحصول على معلومات الاتصال
   */
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      sessionId: this.sessionId,
      reconnectAttempts: this.reconnectAttempts,
      pendingRequests: this.pendingRequests.size,
      browserConnected: !!this.browser,
      pageActive: this.page && !this.page.isClosed()
    };
  }
}

module.exports = QuotexConnector;
